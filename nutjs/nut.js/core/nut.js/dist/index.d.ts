import { AssertClass } from "./lib/assert.class";
import { ClipboardClass } from "./lib/clipboard.class";
import { KeyboardClass, KeyboardConfig } from "./lib/keyboard.class";
import { MouseClass, MouseConfig } from "./lib/mouse.class";
import { ScreenClass, ScreenConfig } from "./lib/screen.class";
import providerRegistry from "./lib/provider/provider-registry.class";
import { ColorQuery, LineQuery, RGBA, WindowQuery, WordQuery } from "@nut-tree/shared";
export { AssertClass, ClipboardClass, KeyboardClass, KeyboardConfig, MouseClass, MouseConfig, ScreenClass, ScreenConfig, providerRegistry };
export { MatchRequest } from "@nut-tree/shared";
export { MatchResult } from "@nut-tree/shared";
export * from "@nut-tree/provider-interfaces";
export * from "@nut-tree/shared";
export { jestMatchers } from "./lib/expect/jest.matcher.function";
export { sleep } from "./lib/sleep.function";
export { centerOf, randomPointIn } from "./lib/location.function";
export { EasingFunction, linear } from "./lib/mouse-movement.function";
export { Window } from "./lib/window.class";
export { useLogger, useConsoleLogger, ConsoleLogLevel } from "./lib/logging.function";
declare const clipboard: ClipboardClass;
declare const keyboard: KeyboardClass;
declare const mouse: MouseClass;
declare const screen: ScreenClass;
declare const assert: AssertClass;
declare const straightTo: (target: import("@nut-tree/shared").Point | Promise<import("@nut-tree/shared").Point>) => Promise<import("@nut-tree/shared").Point[]>, up: (px: number) => Promise<import("@nut-tree/shared").Point[]>, down: (px: number) => Promise<import("@nut-tree/shared").Point[]>, left: (px: number) => Promise<import("@nut-tree/shared").Point[]>, right: (px: number) => Promise<import("@nut-tree/shared").Point[]>;
declare const getWindows: () => Promise<import("./lib/window.class").Window[]>, getActiveWindow: () => Promise<import("./lib/window.class").Window>;
declare const loadImage: (parameters: string) => Promise<import("@nut-tree/shared").Image>;
declare const saveImage: (parameters: import("@nut-tree/provider-interfaces").ImageWriterParameters) => Promise<void>;
declare const imageResource: (fileName: string) => Promise<import("@nut-tree/shared").Image>;
declare const singleWord: (word: string) => WordQuery;
declare const textLine: (line: string) => LineQuery;
declare const windowWithTitle: (title: string | RegExp) => WindowQuery;
declare const pixelWithColor: (color: RGBA) => ColorQuery;
export { fetchFromUrl } from "./lib/imageResources.function";
export { clipboard, keyboard, mouse, screen, assert, straightTo, up, down, left, right, getWindows, getActiveWindow, loadImage, saveImage, imageResource, singleWord, textLine, windowWithTitle, pixelWithColor };
