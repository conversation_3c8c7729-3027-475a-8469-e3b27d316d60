{"version": 3, "file": "bresenham.class.js", "sourceRoot": "", "sources": ["../../../lib/util/bresenham.class.ts"], "names": [], "mappings": ";;;AAAA,6CAAyC;AAEzC,MAAa,SAAS;IACb,MAAM,CAAC,OAAO,CAAC,IAAW,EAAE,EAAS;QAC1C,MAAM,MAAM,GAAY,EAAE,CAAC;QAE3B,IAAI,MAAM,GAAG,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;QAC3B,IAAI,MAAM,GAAG,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;QAE3B,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QACxC,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QACxC,MAAM,UAAU,GAAG,SAAS,CAAC,wBAAwB,CAAC,MAAM,CAAC,CAAC;QAC9D,MAAM,UAAU,GAAG,SAAS,CAAC,wBAAwB,CAAC,MAAM,CAAC,CAAC;QAE9D,IAAI,MAAM,GAAG,CAAC,EAAE,CAAC;YACf,MAAM,GAAG,CAAC,MAAM,CAAC;QACnB,CAAC;QACD,IAAI,MAAM,GAAG,CAAC,EAAE,CAAC;YACf,MAAM,GAAG,CAAC,MAAM,CAAC;QACnB,CAAC;QAED,IAAI,WAAW,EACb,WAAW,EACX,WAAW,EACX,WAAW,EACX,SAAS,EACT,SAAiB,CAAC;QAEpB,IAAI,cAAc,GAAG,cAAc,EAAE,CAAC;YACpC,WAAW,GAAG,UAAU,CAAC;YACzB,WAAW,GAAG,CAAC,CAAC;YAChB,WAAW,GAAG,UAAU,CAAC;YACzB,WAAW,GAAG,UAAU,CAAC;YACzB,SAAS,GAAG,cAAc,CAAC;YAC3B,SAAS,GAAG,cAAc,CAAC;QAC7B,CAAC;aAAM,CAAC;YACN,WAAW,GAAG,CAAC,CAAC;YAChB,WAAW,GAAG,UAAU,CAAC;YACzB,WAAW,GAAG,UAAU,CAAC;YACzB,WAAW,GAAG,UAAU,CAAC;YACzB,SAAS,GAAG,cAAc,CAAC;YAC3B,SAAS,GAAG,cAAc,CAAC;QAC7B,CAAC;QAED,IAAI,KAAK,GAAG,SAAS,GAAG,CAAC,CAAC;QAE1B,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC,EAAE,GAAG,GAAG,SAAS,EAAE,EAAE,GAAG,EAAE,CAAC;YACjE,MAAM,CAAC,IAAI,CAAC,IAAI,cAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YAC7B,KAAK,IAAI,SAAS,CAAC;YACnB,IAAI,KAAK,GAAG,CAAC,EAAE,CAAC;gBACd,KAAK,IAAI,SAAS,CAAC;gBACnB,CAAC,IAAI,WAAW,CAAC;gBACjB,CAAC,IAAI,WAAW,CAAC;YACnB,CAAC;iBAAM,CAAC;gBACN,CAAC,IAAI,WAAW,CAAC;gBACjB,CAAC,IAAI,WAAW,CAAC;YACnB,CAAC;QACH,CAAC;QAED,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAEhB,OAAO,MAAM,CAAC;IAChB,CAAC;IAEO,MAAM,CAAC,wBAAwB,CAAC,CAAS;QAC/C,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpC,CAAC;CACF;AAjED,8BAiEC"}