{"version": 3, "file": "mouse.class.js", "sourceRoot": "", "sources": ["../../lib/mouse.class.ts"], "names": [], "mappings": ";;;AAAA,6CAA0D;AAC1D,qDAAiE;AACjE,uEAA+F;AAkB/F;;GAEG;AACH,MAAa,UAAU;IAMrB;;;OAGG;IACH,YAAoB,gBAAkC;QAAlC,qBAAgB,GAAhB,gBAAgB,CAAkB;QAT/C,WAAM,GAAgB;YAC3B,WAAW,EAAE,GAAG;YAChB,UAAU,EAAE,IAAI;SACjB,CAAC;QAOA,IAAI,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE,EAAE,CAAC;YACrC,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;QACpD,CAAC;IACH,CAAC;IAED;;;OAGG;IACI,KAAK,CAAC,WAAW,CAAC,MAAa;QACpC,IAAI,CAAC,IAAA,gBAAO,EAAC,MAAM,CAAC,EAAE,CAAC;YACrB,MAAM,CAAC,GAAG,IAAI,KAAK,CACjB,8CAA8C,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE,CACvE,CAAC;YACF,IAAI,CAAC,gBAAgB,CAAC,cAAc,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YAChD,MAAM,CAAC,CAAC;QACV,CAAC;QACD,IAAI,CAAC,gBAAgB;aAClB,cAAc,EAAE;aAChB,KAAK,CAAC,wBAAwB,EAAE,MAAM,CAAC,CAAC;QAC3C,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;YAChE,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACX,IAAI,CAAC,gBAAgB,CAAC,cAAc,EAAE,CAAC,KAAK,CAAC,CAAU,CAAC,CAAC;YACzD,MAAM,CAAC,CAAC;QACV,CAAC;IACH,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,WAAW;QACtB,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,gBAAgB;aAChD,QAAQ,EAAE;aACV,oBAAoB,EAAE,CAAC;QAC1B,IAAI,CAAC,gBAAgB;aAClB,cAAc,EAAE;aAChB,KAAK,CAAC,mCAAmC,EAAE,EAAE,eAAe,EAAE,CAAC,CAAC;QACnE,OAAO,eAAe,CAAC;IACzB,CAAC;IAED;;;;OAIG;IACI,KAAK,CAAC,IAAI,CACf,IAAgC,EAChC,eAA+B,gCAAM;QAErC,IAAI,CAAC;YACH,IAAI,SAAS,GAAG,MAAM,IAAI,CAAC;YAC3B,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE,CAAC;gBAC9B,SAAS,GAAG,CAAC,SAAS,CAAC,CAAC;YAC1B,CAAC;YACD,IAAI,CAAC,gBAAgB;iBAClB,cAAc,EAAE;iBAChB,IAAI,CACH,gCAAgC,SAAS,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE,CAClE,CAAC;YACJ,MAAM,SAAS,GAAG,IAAA,oDAA0B,EAC1C,SAAS,CAAC,MAAM,EAChB,IAAI,CAAC,MAAM,CAAC,UAAU,EACtB,YAAY,CACb,CAAC;YACF,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,SAAS,CAAC,MAAM,EAAE,EAAE,GAAG,EAAE,CAAC;gBAChD,MAAM,IAAI,GAAG,SAAS,CAAC,GAAG,CAAC,CAAC;gBAC5B,MAAM,OAAO,GAAG,SAAS,CAAC,GAAG,CAAC,CAAC;gBAC/B,MAAM,IAAA,uCAAsB,EAAC,OAAO,CAAC,CAAC;gBACtC,MAAM,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;YAC/B,CAAC;YACD,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACX,IAAI,CAAC,gBAAgB,CAAC,cAAc,EAAE,CAAC,KAAK,CAAC,CAAU,CAAC,CAAC;YACzD,MAAM,CAAC,CAAC;QACV,CAAC;IACH,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,SAAS;QACpB,OAAO,IAAI,CAAC,KAAK,CAAC,eAAM,CAAC,IAAI,CAAC,CAAC;IACjC,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,UAAU;QACrB,OAAO,IAAI,CAAC,KAAK,CAAC,eAAM,CAAC,KAAK,CAAC,CAAC;IAClC,CAAC;IAED;;;;OAIG;IACI,KAAK,CAAC,UAAU,CAAC,MAAc;QACpC,IAAI,CAAC;YACH,MAAM,IAAA,sBAAK,EAAC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;YACrC,MAAM,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;YAC1D,IAAI,CAAC,gBAAgB;iBAClB,cAAc,EAAE;iBAChB,IAAI,CAAC,iBAAiB,MAAM,QAAQ,CAAC,CAAC;YACzC,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACX,IAAI,CAAC,gBAAgB,CAAC,cAAc,EAAE,CAAC,KAAK,CAAC,CAAU,CAAC,CAAC;YACzD,MAAM,CAAC,CAAC;QACV,CAAC;IACH,CAAC;IAED;;;;OAIG;IACI,KAAK,CAAC,QAAQ,CAAC,MAAc;QAClC,IAAI,CAAC;YACH,MAAM,IAAA,sBAAK,EAAC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;YACrC,MAAM,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;YACxD,IAAI,CAAC,gBAAgB;iBAClB,cAAc,EAAE;iBAChB,IAAI,CAAC,eAAe,MAAM,QAAQ,CAAC,CAAC;YACvC,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACX,IAAI,CAAC,gBAAgB,CAAC,cAAc,EAAE,CAAC,KAAK,CAAC,CAAU,CAAC,CAAC;YACzD,MAAM,CAAC,CAAC;QACV,CAAC;IACH,CAAC;IAED;;;;OAIG;IACI,KAAK,CAAC,UAAU,CAAC,MAAc;QACpC,IAAI,CAAC;YACH,MAAM,IAAA,sBAAK,EAAC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;YACrC,MAAM,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;YAC1D,IAAI,CAAC,gBAAgB;iBAClB,cAAc,EAAE;iBAChB,IAAI,CAAC,iBAAiB,MAAM,QAAQ,CAAC,CAAC;YACzC,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACX,IAAI,CAAC,gBAAgB,CAAC,cAAc,EAAE,CAAC,KAAK,CAAC,CAAU,CAAC,CAAC;YACzD,MAAM,CAAC,CAAC;QACV,CAAC;IACH,CAAC;IAED;;;;OAIG;IACI,KAAK,CAAC,WAAW,CAAC,MAAc;QACrC,IAAI,CAAC;YACH,MAAM,IAAA,sBAAK,EAAC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;YACrC,MAAM,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;YAC3D,IAAI,CAAC,gBAAgB;iBAClB,cAAc,EAAE;iBAChB,IAAI,CAAC,kBAAkB,MAAM,QAAQ,CAAC,CAAC;YAC1C,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACX,IAAI,CAAC,gBAAgB,CAAC,cAAc,EAAE,CAAC,KAAK,CAAC,CAAU,CAAC,CAAC;YACzD,MAAM,CAAC,CAAC;QACV,CAAC;IACH,CAAC;IAED;;;;OAIG;IACI,KAAK,CAAC,IAAI,CAAC,IAAgC;QAChD,IAAI,CAAC;YACH,MAAM,IAAA,sBAAK,EAAC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;YACrC,MAAM,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE,CAAC,WAAW,CAAC,eAAM,CAAC,IAAI,CAAC,CAAC;YAChE,IAAI,CAAC,gBAAgB,CAAC,cAAc,EAAE,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;YACzE,MAAM,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACtB,MAAM,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE,CAAC,aAAa,CAAC,eAAM,CAAC,IAAI,CAAC,CAAC;YAClE,IAAI,CAAC,gBAAgB,CAAC,cAAc,EAAE,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;YAC1E,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACX,IAAI,CAAC,gBAAgB,CAAC,cAAc,EAAE,CAAC,KAAK,CAAC,CAAU,CAAC,CAAC;YACzD,MAAM,CAAC,CAAC;QACV,CAAC;IACH,CAAC;IAED;;;OAGG;IACI,KAAK,CAAC,WAAW,CAAC,GAAW;QAClC,IAAI,CAAC;YACH,MAAM,IAAA,sBAAK,EAAC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;YACrC,MAAM,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;YACxD,MAAM,OAAO,GAAG,eAAM,CAAC,GAAG,CAAC,CAAC;YAC5B,IAAI,CAAC,gBAAgB;iBAClB,cAAc,EAAE;iBAChB,IAAI,CAAC,WAAW,OAAO,eAAe,CAAC,CAAC;YAC3C,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACX,IAAI,CAAC,gBAAgB,CAAC,cAAc,EAAE,CAAC,KAAK,CAAC,CAAU,CAAC,CAAC;YACzD,MAAM,CAAC,CAAC;QACV,CAAC;IACH,CAAC;IAED;;;OAGG;IACI,KAAK,CAAC,aAAa,CAAC,GAAW;QACpC,IAAI,CAAC;YACH,MAAM,IAAA,sBAAK,EAAC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;YACrC,MAAM,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;YAC1D,MAAM,OAAO,GAAG,eAAM,CAAC,GAAG,CAAC,CAAC;YAC5B,IAAI,CAAC,gBAAgB;iBAClB,cAAc,EAAE;iBAChB,IAAI,CAAC,WAAW,OAAO,eAAe,CAAC,CAAC;YAC3C,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACX,IAAI,CAAC,gBAAgB,CAAC,cAAc,EAAE,CAAC,KAAK,CAAC,CAAU,CAAC,CAAC;YACzD,MAAM,CAAC,CAAC;QACV,CAAC;IACH,CAAC;IAED;;;OAGG;IACI,KAAK,CAAC,KAAK,CAAC,GAAW;QAC5B,IAAI,CAAC;YACH,MAAM,IAAA,sBAAK,EAAC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;YACrC,MAAM,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YAClD,MAAM,OAAO,GAAG,eAAM,CAAC,GAAG,CAAC,CAAC;YAC5B,IAAI,CAAC,gBAAgB;iBAClB,cAAc,EAAE;iBAChB,IAAI,CAAC,WAAW,OAAO,eAAe,CAAC,CAAC;YAC3C,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACX,IAAI,CAAC,gBAAgB,CAAC,cAAc,EAAE,CAAC,KAAK,CAAC,CAAU,CAAC,CAAC;YACzD,MAAM,CAAC,CAAC;QACV,CAAC;IACH,CAAC;IAED;;;OAGG;IACI,KAAK,CAAC,WAAW,CAAC,GAAW;QAClC,IAAI,CAAC;YACH,MAAM,IAAA,sBAAK,EAAC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;YACrC,MAAM,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;YACxD,MAAM,OAAO,GAAG,eAAM,CAAC,GAAG,CAAC,CAAC;YAC5B,IAAI,CAAC,gBAAgB;iBAClB,cAAc,EAAE;iBAChB,IAAI,CAAC,WAAW,OAAO,eAAe,CAAC,CAAC;YAC3C,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACX,IAAI,CAAC,gBAAgB,CAAC,cAAc,EAAE,CAAC,KAAK,CAAC,CAAU,CAAC,CAAC;YACzD,MAAM,CAAC,CAAC;QACV,CAAC;IACH,CAAC;CACF;AAnRD,gCAmRC"}