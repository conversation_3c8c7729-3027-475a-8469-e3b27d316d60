{"version": 3, "file": "window.class.js", "sourceRoot": "", "sources": ["../../lib/window.class.ts"], "names": [], "mappings": ";;;AAAA,6CAa0B;AAE1B,8DAAkD;AAElD,MAAa,MAAM;IAGjB,YACU,gBAAkC,EAClC,YAAoB;QADpB,qBAAgB,GAAhB,gBAAgB,CAAkB;QAClC,iBAAY,GAAZ,YAAY,CAAQ;QAE5B,IAAI,CAAC,SAAS,GAAG,IAAI,GAAG,EAGrB,CAAC;IACN,CAAC;IAED,IAAI,KAAK;QACP,OAAO,IAAI,CAAC,QAAQ,EAAE,CAAC;IACzB,CAAC;IAED,KAAK,CAAC,QAAQ;QACZ,OAAO,IAAI,CAAC,gBAAgB,CAAC,SAAS,EAAE,CAAC,cAAc,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;IAC7E,CAAC;IAED,IAAI,MAAM;QACR,OAAO,IAAI,CAAC,SAAS,EAAE,CAAC;IAC1B,CAAC;IAED,KAAK,CAAC,SAAS;QACb,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,SAAS,EAAE,CAAC,eAAe,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAC1F,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,SAAS,EAAE,CAAC,UAAU,EAAE,CAAC;QAC9E,IAAI,MAAM,CAAC,IAAI,GAAG,CAAC,EAAE,CAAC;YACpB,MAAM,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC;YAC1C,MAAM,CAAC,IAAI,GAAG,CAAC,CAAC;QAClB,CAAC;QACD,IAAI,MAAM,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC;YACnB,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC,GAAG,CAAC;YAC3C,MAAM,CAAC,GAAG,GAAG,CAAC,CAAC;QACjB,CAAC;QACD,MAAM,gBAAgB,GAAG,MAAM,CAAC,IAAI,GAAG,MAAM,CAAC,KAAK,CAAC;QACpD,IAAI,gBAAgB,GAAG,gBAAgB,CAAC,KAAK,EAAE,CAAC;YAC9C,MAAM,WAAW,GAAG,gBAAgB,GAAG,gBAAgB,CAAC,KAAK,CAAC;YAC9D,MAAM,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,GAAG,WAAW,CAAC;QAC5C,CAAC;QACD,MAAM,iBAAiB,GAAG,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC;QACrD,IAAI,iBAAiB,GAAG,gBAAgB,CAAC,MAAM,EAAE,CAAC;YAChD,MAAM,YAAY,GAAG,iBAAiB,GAAG,gBAAgB,CAAC,MAAM,CAAC;YACjE,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,GAAG,YAAY,CAAC;QAC/C,CAAC;QACD,IAAI,MAAM,CAAC,KAAK,GAAG,CAAC,EAAE,CAAC;YACrB,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC;QACnB,CAAC;QACD,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACtB,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;QACpB,CAAC;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,KAAK,CAAC,IAAI,CAAC,SAAgB;QACzB,OAAO,IAAI,CAAC,gBAAgB;aACzB,SAAS,EAAE;aACX,UAAU,CAAC,IAAI,CAAC,YAAY,EAAE,SAAS,CAAC,CAAC;IAC9C,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,OAAa;QACxB,OAAO,IAAI,CAAC,gBAAgB;aACzB,SAAS,EAAE;aACX,YAAY,CAAC,IAAI,CAAC,YAAY,EAAE,OAAO,CAAC,CAAC;IAC9C,CAAC;IAED,KAAK,CAAC,KAAK;QACT,OAAO,IAAI,CAAC,gBAAgB,CAAC,SAAS,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;IAC1E,CAAC;IAED,KAAK,CAAC,QAAQ;QACZ,OAAO,IAAI,CAAC,gBAAgB,CAAC,SAAS,EAAE,CAAC,cAAc,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;IAC7E,CAAC;IAED,KAAK,CAAC,OAAO;QACX,OAAO,IAAI,CAAC,gBAAgB,CAAC,SAAS,EAAE,CAAC,aAAa,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;IAC5E,CAAC;IAED,KAAK,CAAC,WAAW,CAAC,WAAoB;QACpC,OAAO,IAAI,CAAC,gBAAgB,CAAC,yBAAyB,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,YAAY,EAAE,WAAW,CAAC,CAAC;IACvG,CAAC;IAED;;;OAGG;IACI,KAAK,CAAC,IAAI,CACf,WAAiF;QAEjF,MAAM,MAAM,GAAG,MAAM,WAAW,CAAC;QACjC,IAAI,CAAC,gBAAgB,CAAC,cAAc,EAAE,CAAC,IAAI,CAAC,iBAAiB,MAAM,cAAc,IAAI,CAAC,YAAY,EAAE,CAAC,CAAC;QAEtG,IAAI,CAAC;YACH,IAAI,IAAA,6BAAoB,EAAC,MAAM,CAAC,EAAE,CAAC;gBACjC,IAAI,CAAC,gBAAgB,CAAC,cAAc,EAAE,CAAC,KAAK,CAAC,iCAAiC,CAAC,CAAC;gBAChF,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,gBAAgB;qBAC9C,yBAAyB,EAAE;qBAC3B,WAAW,CAAC,IAAI,CAAC,YAAY,EAAE,MAAM,CAAC,EAAE,CAAC,WAAW,CAAC,CAAC;gBACzD,MAAM,aAAa,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;gBAC1D,IAAI,CAAC,gBAAgB;qBAClB,cAAc,EAAE;qBAChB,KAAK,CAAC,GAAG,aAAa,CAAC,MAAM,4BAA4B,CAAC,CAAC;gBAC9D,KAAK,MAAM,IAAI,IAAI,aAAa,EAAE,CAAC;oBACjC,IAAI,CAAC,gBAAgB,CAAC,cAAc,EAAE,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC;oBAC/D,MAAM,IAAI,CAAC,aAAa,CAAC,CAAC;gBAC5B,CAAC;gBACD,OAAO,aAAa,CAAC;YACvB,CAAC;YACD,MAAM,IAAI,KAAK,CACb,sEAAsE,CACvE,CAAC;QACJ,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACX,MAAM,KAAK,GAAG,IAAI,KAAK,CACrB,iBAAiB,MAAM,CAAC,EAAE,qBAAqB,CAAC,GAAG,CACpD,CAAC;YACF,IAAI,CAAC,gBAAgB,CAAC,cAAc,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YACpD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;;OAGG;IACI,KAAK,CAAC,OAAO,CAClB,WAAiF;QAEjF,MAAM,MAAM,GAAG,MAAM,WAAW,CAAC;QACjC,IAAI,CAAC,gBAAgB,CAAC,cAAc,EAAE,CAAC,IAAI,CAAC,iBAAiB,MAAM,cAAc,IAAI,CAAC,YAAY,EAAE,CAAC,CAAC;QAEtG,IAAI,CAAC;YACH,IAAI,IAAA,6BAAoB,EAAC,MAAM,CAAC,EAAE,CAAC;gBACjC,IAAI,CAAC,gBAAgB,CAAC,cAAc,EAAE,CAAC,KAAK,CAAC,iCAAiC,CAAC,CAAC;gBAChF,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,gBAAgB;qBAC/C,yBAAyB,EAAE;qBAC3B,YAAY,CAAC,IAAI,CAAC,YAAY,EAAE,MAAM,CAAC,EAAE,CAAC,WAAW,CAAC,CAAC;gBAC1D,MAAM,aAAa,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;gBAC1D,IAAI,CAAC,gBAAgB;qBAClB,cAAc,EAAE;qBAChB,KAAK,CAAC,GAAG,aAAa,CAAC,MAAM,4BAA4B,CAAC,CAAC;gBAC9D,KAAK,MAAM,IAAI,IAAI,aAAa,EAAE,CAAC;oBACjC,KAAK,MAAM,aAAa,IAAI,cAAc,EAAE,CAAC;wBAC3C,IAAI,CAAC,gBAAgB,CAAC,cAAc,EAAE,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC;wBAC/D,MAAM,IAAI,CAAC,aAAa,CAAC,CAAC;oBAC5B,CAAC;gBACH,CAAC;gBACD,OAAO,cAAc,CAAC;YACxB,CAAC;YACD,MAAM,IAAI,KAAK,CACb,sEAAsE,CACvE,CAAC;QACJ,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACX,MAAM,KAAK,GAAG,IAAI,KAAK,CACrB,iBAAiB,MAAM,CAAC,EAAE,qBAAqB,CAAC,GAAG,CACpD,CAAC;YACF,IAAI,CAAC,gBAAgB,CAAC,cAAc,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YACpD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;;;;;OAMG;IACI,KAAK,CAAC,OAAO,CAClB,WAA6D,EAC7D,SAAkB,EAClB,cAAuB,EACvB,MAAqD;QAErD,MAAM,MAAM,GAAG,MAAM,WAAW,CAAC;QAEjC,MAAM,YAAY,GAAG,SAAS,aAAT,SAAS,cAAT,SAAS,GAAI,IAAI,CAAC;QACvC,MAAM,mBAAmB,GAAG,cAAc,aAAd,cAAc,cAAd,cAAc,GAAI,GAAG,CAAC;QAElD,IAAI,CAAC,gBAAgB;aAClB,cAAc,EAAE;aAChB,IAAI,CACH,eAAe,MAAM,CAAC,EAAE,kCACtB,YAAY,GAAG,IACjB,uBAAuB,mBAAmB,KAAK,CAChD,CAAC;QACJ,OAAO,IAAA,0BAAO,EACZ,mBAAmB,EACnB,YAAY,EACZ,GAAG,EAAE;YACH,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAC3B,CAAC,EACD;YACE,MAAM,EAAE,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,KAAK;SACtB,CACF,CAAC;IACJ,CAAC;IAED;;;;OAIG;IACI,EAAE,CAAC,WAA+B,EAAE,QAA+B;QACxE,MAAM,aAAa,GAAG,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC;QACzD,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC,GAAG,aAAa,EAAE,QAAQ,CAAC,CAAC,CAAC;QAC9D,IAAI,CAAC,gBAAgB;aAClB,cAAc,EAAE;aAChB,IAAI,CACH,iCAAiC,WAAW,CAAC,EAAE,yBAC7C,aAAa,CAAC,MAAM,GAAG,CACzB,mBAAmB,CACpB,CAAC;IACN,CAAC;IAEO,gBAAgB,CACtB,KAAyB;;QAEzB,OAAO,MAAA,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,KAAK,CAAC,mCAAI,EAAE,CAAC;IACzC,CAAC;CACF;AA5ND,wBA4NC"}