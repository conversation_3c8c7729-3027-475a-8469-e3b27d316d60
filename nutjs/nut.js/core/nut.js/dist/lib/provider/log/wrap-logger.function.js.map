{"version": 3, "file": "wrap-logger.function.js", "sourceRoot": "", "sources": ["../../../../lib/provider/log/wrap-logger.function.ts"], "names": [], "mappings": ";;;AAEA,MAAM,aAAa,GAAG,UAAU,CAAC;AACjC,MAAM,cAAc,GAAG,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;AAC1D,MAAM,WAAW,GAAG,CAAC,OAAO,CAAC,CAAC;AAI9B,SAAgB,UAAU,CAAC,cAAoC;IAC7D,KAAK,MAAM,KAAK,IAAI,cAAc,EAAE,CAAC;QACnC,MAAM,cAAc,GAAG,cAAc,CAAC,KAA6B,CAAC,CAAC;QACrE,cAAc,CAAC,KAA6B,CAAC,GAAG,CAAC,OAAe,EAAE,IAAS,EAAE,EAAE;YAC7E,MAAM,cAAc,GAAG,GAAG,aAAa,MAAM,OAAO,EAAE,CAAC;YACvD,cAAc,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC;QACvC,CAAC,CAAC;IACJ,CAAC;IACD,KAAK,MAAM,KAAK,IAAI,WAAW,EAAE,CAAC;QAChC,MAAM,cAAc,GAAG,cAAc,CAAC,KAA0B,CAAC,CAAC;QAClE,cAAc,CAAC,KAA0B,CAAC,GAAG,CAAC,OAAc,EAAE,IAAS,EAAE,EAAE;YACzE,OAAO,CAAC,OAAO,GAAG,GAAG,aAAa,MAAM,OAAO,EAAE,CAAC;YAClD,cAAc,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;QAChC,CAAC,CAAC;IACJ,CAAC;IAED,OAAO,cAAc,CAAC;AACxB,CAAC;AAjBD,gCAiBC"}