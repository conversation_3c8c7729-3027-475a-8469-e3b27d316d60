{"version": 3, "file": "provider-registry.class.js", "sourceRoot": "", "sources": ["../../../lib/provider/provider-registry.class.ts"], "names": [], "mappings": ";;;;;AAkBA,2FAA2D;AAC3D,2FAA2D;AAC3D,oGAAoE;AACpE,2EAAgE;AAChE,oFAAyD;AACzD,4CAOsB;AACtB,qEAAwD;AAGxD,MAAM,uBAAuB;IAA7B;QAoBE,iBAAY,GAAG,GAA+B,EAAE;YAC9C,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;gBACpB,OAAO,IAAI,CAAC,UAAU,CAAC;YACzB,CAAC;YACD,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAC;YAC3D,IAAI,CAAC,cAAc,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YACnC,MAAM,KAAK,CAAC;QACd,CAAC,CAAC;QAEF,8BAAyB,GAAG,CAAC,KAAiC,EAAE,EAAE;YAChE,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;YACxB,IAAI,CAAC,cAAc,EAAE,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC;QAC1E,CAAC,CAAC;QAMF,mBAAc,GAAG,GAAyB,EAAE;YAC1C,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;gBACtB,OAAO,IAAI,CAAC,YAAY,CAAC;YAC3B,CAAC;YACD,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;YACrD,IAAI,CAAC,cAAc,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YACnC,MAAM,KAAK,CAAC;QACd,CAAC,CAAC;QAEF,wBAAmB,GAAG,CAAC,KAA2B,EAAE,EAAE;YACpD,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;YAC1B,IAAI,CAAC,cAAc,EAAE,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;QACpE,CAAC,CAAC;QAMF,gBAAW,GAAG,GAA8B,EAAE;YAC5C,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;gBACnB,OAAO,IAAI,CAAC,SAAS,CAAC;YACxB,CAAC;YACD,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;YAC1D,IAAI,CAAC,cAAc,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YACnC,MAAM,KAAK,CAAC;QACd,CAAC,CAAC;QAEF,6BAAwB,GAAG,CAAC,KAAgC,EAAE,EAAE;YAC9D,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;YACvB,IAAI,CAAC,cAAc,EAAE,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;QACzE,CAAC,CAAC;QAMF,aAAQ,GAAG,GAA2B,EAAE;YACtC,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;gBAChB,OAAO,IAAI,CAAC,MAAM,CAAC;YACrB,CAAC;YACD,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAC;YACvD,IAAI,CAAC,cAAc,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YACnC,MAAM,KAAK,CAAC;QACd,CAAC,CAAC;QAEF,0BAAqB,GAAG,CAAC,KAA6B,EAAE,EAAE;YACxD,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;YACpB,IAAI,CAAC,cAAc,EAAE,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAC;QACtE,CAAC,CAAC;QAMF,cAAS,GAAG,GAA4B,EAAE;YACxC,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;gBACjB,OAAO,IAAI,CAAC,OAAO,CAAC;YACtB,CAAC;YACD,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAC;YACxD,IAAI,CAAC,cAAc,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YACnC,MAAM,KAAK,CAAC;QACd,CAAC,CAAC;QAEF,2BAAsB,GAAG,CAAC,KAA8B,EAAE,EAAE;YAC1D,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;YACrB,IAAI,CAAC,cAAc,EAAE,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAC;QACvE,CAAC,CAAC;QAMF,cAAS,GAAG,GAA4B,EAAE;YACxC,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;gBACjB,OAAO,IAAI,CAAC,OAAO,CAAC;YACtB,CAAC;YACD,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAC;YACxD,IAAI,CAAC,cAAc,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YACnC,MAAM,KAAK,CAAC;QACd,CAAC,CAAC;QAEF,2BAAsB,GAAG,CAAC,KAA8B,EAAE,EAAE;YAC1D,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;YACrB,IAAI,CAAC,cAAc,EAAE,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAC;QACvE,CAAC,CAAC;QAMF,kBAAa,GAAG,GAAwB,EAAE;YACxC,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;gBACrB,OAAO,IAAI,CAAC,WAAW,CAAC;YAC1B,CAAC;YACD,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;YACpD,IAAI,CAAC,cAAc,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YACnC,MAAM,KAAK,CAAC;QACd,CAAC,CAAC;QAEF,uBAAkB,GAAG,CAAC,KAA0B,EAAE,EAAE;YAClD,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;YACzB,IAAI,CAAC,cAAc,EAAE,CAAC,KAAK,CAAC,oCAAoC,EAAE,KAAK,CAAC,CAAC;QAC3E,CAAC,CAAC;QAMF,oBAAe,GAAG,GAA0B,EAAE;YAC5C,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;gBACvB,OAAO,IAAI,CAAC,aAAa,CAAC;YAC5B,CAAC;YACD,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAC;YACtD,IAAI,CAAC,cAAc,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YACnC,MAAM,KAAK,CAAC;QACd,CAAC,CAAC;QAEF,yBAAoB,GAAG,CAAC,KAA4B,EAAE,EAAE;YACtD,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;YAC3B,IAAI,CAAC,cAAc,EAAE,CAAC,KAAK,CAAC,sCAAsC,EAAE,KAAK,CAAC,CAAC;QAC7E,CAAC,CAAC;QAEF,8BAAyB,GAAG,GAAuC,EAAE;YACnE,IAAI,IAAI,CAAC,uBAAuB,EAAE,CAAC;gBACjC,OAAO,IAAI,CAAC,uBAAuB,CAAC;YACtC,CAAC;YACD,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,sCAAsC,CAAC,CAAC;YAChE,IAAI,CAAC,cAAc,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YACnC,MAAM,KAAK,CAAC;QACd,CAAC,CAAC;QAEF,mCAA8B,GAAG,CAAC,KAAyC,EAAE,EAAE;YAC7E,IAAI,CAAC,uBAAuB,GAAG,KAAK,CAAC;YACrC,IAAI,CAAC,cAAc,EAAE,CAAC,KAAK,CAAC,gDAAgD,EAAE,KAAK,CAAC,CAAC;QACvF,CAAC,CAAC;QAMF,mBAAc,GAAG,GAAgB,EAAE;YACjC,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;gBACtB,OAAO,IAAI,CAAC,YAAY,CAAC;YAC3B,CAAC;YACD,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;YACrD,IAAI,CAAC,cAAc,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YACnC,MAAM,KAAK,CAAC;QACd,CAAC,CAAC;QAEF,wBAAmB,GAAG,CAAC,KAAkB,EAAE,EAAE;YAC3C,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;YAC1B,IAAI,CAAC,cAAc,EAAE,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;QACpE,CAAC,CAAC;QAMF,mBAAc,GAAG,GAAgB,EAAE;YACjC,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;gBACtB,OAAO,IAAI,CAAC,YAAY,CAAC;YAC3B,CAAC;YACD,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;YACrD,IAAI,CAAC,cAAc,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YACnC,MAAM,KAAK,CAAC;QACd,CAAC,CAAC;QAEF,wBAAmB,GAAG,CAAC,KAAkB,EAAE,EAAE;YAC3C,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;YAC1B,IAAI,CAAC,cAAc,EAAE,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;QACpE,CAAC,CAAC;QAMF,sBAAiB,GAAG,GAAmB,EAAE;YACvC,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;gBACzB,OAAO,IAAI,CAAC,eAAe,CAAC;YAC9B,CAAC;YACD,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAC;YACxD,IAAI,CAAC,cAAc,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YACnC,MAAM,KAAK,CAAC;QACd,CAAC,CAAC;QAEF,2BAAsB,GAAG,CAAC,KAAqB,EAAQ,EAAE;YACvD,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC;YAC7B,IAAI,CAAC,cAAc,EAAE,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAC;QACvE,CAAC,CAAC;QAMF,mBAAc,GAAG,GAAyB,EAAE;YAC1C,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;gBACtB,OAAO,IAAI,CAAC,YAAY,CAAC;YAC3B,CAAC;YAED,6CAA6C;YAC7C,OAAO,IAAI,yCAAe,EAAE,CAAC;QAC/B,CAAC,CAAC;QAEF,wBAAmB,GAAG,CAAC,KAA2B,EAAQ,EAAE;YAC1D,IAAI,CAAC,YAAY,GAAG,IAAA,iCAAU,EAAC,KAAK,CAAC,CAAC;YACtC,IAAI,CAAC,cAAc,EAAE,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;QACpE,CAAC,CAAC;QAMF,mBAAc,GAAG,GAAyB,EAAE;YAC1C,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;gBACtB,OAAO,IAAI,CAAC,YAAY,CAAC;YAC3B,CAAC;YACD,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;YACrD,IAAI,CAAC,cAAc,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YACnC,MAAM,KAAK,CAAC;QACd,CAAC,CAAC;QAEF,wBAAmB,GAAG,CAAC,KAA2B,EAAQ,EAAE;YAC1D,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;YAC1B,IAAI,CAAC,cAAc,EAAE,CAAC,KAAK,CAAC,qCAAqC,EAAE,KAAK,CAAC,CAAC;QAC5E,CAAC,CAAC;IACJ,CAAC;IAvPC,YAAY;QACV,OAAO,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC;IACjC,CAAC;IAgBD,cAAc;QACZ,OAAO,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC;IACnC,CAAC;IAgBD,WAAW;QACT,OAAO,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC;IAChC,CAAC;IAgBD,QAAQ;QACN,OAAO,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC;IAC7B,CAAC;IAgBD,SAAS;QACP,OAAO,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC;IAC9B,CAAC;IAgBD,SAAS;QACP,OAAO,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC;IAC9B,CAAC;IAgBD,aAAa;QACX,OAAO,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC;IAClC,CAAC;IAgBD,eAAe;QACb,OAAO,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC;IACpC,CAAC;IA8BD,cAAc;QACZ,OAAO,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC;IACnC,CAAC;IAgBD,cAAc;QACZ,OAAO,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC;IACnC,CAAC;IAgBD,iBAAiB;QACf,OAAO,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC;IACtC,CAAC;IAgBD,cAAc;QACZ,OAAO,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC;IACnC,CAAC;IAgBD,cAAc;QACZ,OAAO,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC;IACnC,CAAC;CAeF;AAED,MAAM,gBAAgB,GAAG,IAAI,uBAAuB,EAAE,CAAC;AAEvD,gBAAgB,CAAC,mBAAmB,CAAC,IAAI,iCAAe,EAAE,CAAC,CAAC;AAC5D,gBAAgB,CAAC,mBAAmB,CAAC,IAAI,iCAAe,EAAE,CAAC,CAAC;AAC5D,gBAAgB,CAAC,sBAAsB,CAAC,IAAI,oCAAkB,EAAE,CAAC,CAAC;AAClE,gBAAgB,CAAC,mBAAmB,CAAC,IAAI,4BAAe,EAAE,CAAC,CAAC;AAC5D,gBAAgB,CAAC,mBAAmB,CAAC,IAAI,yCAAe,EAAE,CAAC,CAAC;AAE5D,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,6CAAiC,CAAC,EAAE,CAAC;IACpD,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,sDAA0C,CAAC,EAAE,CAAC;QAC7D,MAAM,SAAS,GAAG,OAAO,CAAC,sCAAsC,CAAC,CAAC,OAAO,CAAC;QAC1E,gBAAgB,CAAC,yBAAyB,CAAC,IAAI,SAAS,EAAE,CAAC,CAAC;IAC9D,CAAC;IACD,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,qDAAyC,CAAC,EAAE,CAAC;QAC5D,MAAM,EAAE,qBAAqB,EAAE,GAAG,OAAO,CAAC,kBAAkB,CAAC,CAAC;QAC9D,gBAAgB,CAAC,wBAAwB,CAAC,IAAI,qBAAqB,EAAE,CAAC,CAAC;IACzE,CAAC;IACD,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,kDAAsC,CAAC,EAAE,CAAC;QACzD,MAAM,EAAE,kBAAkB,EAAE,GAAG,OAAO,CAAC,kBAAkB,CAAC,CAAC;QAC3D,gBAAgB,CAAC,qBAAqB,CAAC,IAAI,kBAAkB,EAAE,CAAC,CAAC;IACnE,CAAC;IACD,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,mDAAuC,CAAC,EAAE,CAAC;QAC1D,MAAM,EAAE,mBAAmB,EAAE,GAAG,OAAO,CAAC,kBAAkB,CAAC,CAAC;QAC5D,gBAAgB,CAAC,sBAAsB,CAAC,IAAI,mBAAmB,EAAE,CAAC,CAAC;IACrE,CAAC;IACD,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,mDAAuC,CAAC,EAAE,CAAC;QAC1D,MAAM,EAAE,mBAAmB,EAAE,GAAG,OAAO,CAAC,kBAAkB,CAAC,CAAC;QAC5D,gBAAgB,CAAC,sBAAsB,CAAC,IAAI,mBAAmB,EAAE,CAAC,CAAC;IACrE,CAAC;AACH,CAAC;AAED,kBAAe,gBAAgB,CAAC"}