import { ClipboardProviderInterface, ColorFinderInterface, ImageFinderInterface, ImageProcessor, ImageReader, ImageWriter, KeyboardProviderInterface, LogProviderInterface, MouseProviderInterface, ProviderRegistry, ScreenProviderInterface, TextFinderInterface, WindowFinderInterface, WindowProviderInterface, ElementInspectionProviderInterface } from "@nut-tree/provider-interfaces";
declare class DefaultProviderRegistry implements ProviderRegistry {
    private _clipboard?;
    private _imageFinder?;
    private _keyboard?;
    private _mouse?;
    private _screen?;
    private _window?;
    private _imageReader?;
    private _imageWriter?;
    private _imageProcessor?;
    private _logProvider?;
    private _textFinder?;
    private _windowFinder?;
    private _colorFinder?;
    private _windowElementInspector?;
    hasClipboard(): boolean;
    getClipboard: () => ClipboardProviderInterface;
    registerClipboardProvider: (value: ClipboardProviderInterface) => void;
    hasImageFinder(): boolean;
    getImageFinder: () => ImageFinderInterface;
    registerImageFinder: (value: ImageFinderInterface) => void;
    hasKeyboard(): boolean;
    getKeyboard: () => KeyboardProviderInterface;
    registerKeyboardProvider: (value: KeyboardProviderInterface) => void;
    hasMouse(): boolean;
    getMouse: () => MouseProviderInterface;
    registerMouseProvider: (value: MouseProviderInterface) => void;
    hasScreen(): boolean;
    getScreen: () => ScreenProviderInterface;
    registerScreenProvider: (value: ScreenProviderInterface) => void;
    hasWindow(): boolean;
    getWindow: () => WindowProviderInterface;
    registerWindowProvider: (value: WindowProviderInterface) => void;
    hasTextFinder(): boolean;
    getTextFinder: () => TextFinderInterface;
    registerTextFinder: (value: TextFinderInterface) => void;
    hasWindowFinder(): boolean;
    getWindowFinder: () => WindowFinderInterface;
    registerWindowFinder: (value: WindowFinderInterface) => void;
    getWindowElementInspector: () => ElementInspectionProviderInterface;
    registerWindowElementInspector: (value: ElementInspectionProviderInterface) => void;
    hasImageReader(): boolean;
    getImageReader: () => ImageReader;
    registerImageReader: (value: ImageReader) => void;
    hasImageWriter(): boolean;
    getImageWriter: () => ImageWriter;
    registerImageWriter: (value: ImageWriter) => void;
    hasImageProcessor(): boolean;
    getImageProcessor: () => ImageProcessor;
    registerImageProcessor: (value: ImageProcessor) => void;
    hasLogProvider(): boolean;
    getLogProvider: () => LogProviderInterface;
    registerLogProvider: (value: LogProviderInterface) => void;
    hasColorFinder(): boolean;
    getColorFinder: () => ColorFinderInterface;
    registerColorFinder: (value: ColorFinderInterface) => void;
}
declare const providerRegistry: DefaultProviderRegistry;
export default providerRegistry;
