{"version": 3, "file": "keyboard.class.js", "sourceRoot": "", "sources": ["../../lib/keyboard.class.ts"], "names": [], "mappings": ";;;AAAA,6CAAuC;AACvC,qDAAyC;AAKzC,MAAM,aAAa,GAAG,CAAC,KAAuB,EAAqB,EAAE;IACnE,OAAO,KAAK,CAAC,KAAK,CAAC,CAAC,IAAkB,EAAE,EAAE,CAAC,OAAO,IAAI,KAAK,QAAQ,CAAC,CAAC;AACvE,CAAC,CAAC;AAYF;;GAEG;AACH,MAAa,aAAa;IAQxB;;;OAGG;IACH,YAAoB,gBAAkC;QAAlC,qBAAgB,GAAhB,gBAAgB,CAAkB;QAXtD;;WAEG;QACI,WAAM,GAAmB;YAC9B,WAAW,EAAE,GAAG;SACjB,CAAC;QAOA,IAAI,IAAI,CAAC,gBAAgB,CAAC,WAAW,EAAE,EAAE,CAAC;YACxC,IAAI,CAAC,gBAAgB;iBAClB,WAAW,EAAE;iBACb,gBAAgB,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;QAC/C,CAAC;IACH,CAAC;IAED;;;;;;;;;OASG;IACI,KAAK,CAAC,IAAI,CAAC,GAAG,KAAkB;QACrC,IAAI,CAAC;YACH,IAAI,aAAa,CAAC,KAAK,CAAC,EAAE,CAAC;gBACzB,KAAK,MAAM,IAAI,IAAI,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;oBACnC,MAAM,IAAA,sBAAK,EAAC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;oBACrC,MAAM,IAAI,CAAC,gBAAgB,CAAC,WAAW,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;oBACrD,IAAI,CAAC,gBAAgB,CAAC,cAAc,EAAE,CAAC,KAAK,CAAC,UAAU,IAAI,EAAE,CAAC,CAAC;gBACjE,CAAC;gBACD,IAAI,CAAC,gBAAgB,CAAC,cAAc,EAAE,CAAC,IAAI,CAAC,gBAAgB,KAAK,EAAE,CAAC,CAAC;YACvE,CAAC;iBAAM,CAAC;gBACN,MAAM,IAAI,CAAC,gBAAgB,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,GAAI,KAAe,CAAC,CAAC;gBACrE,MAAM,GAAG,GAAG,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBACpC,MAAM,SAAS,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;gBACrC,MAAM,OAAO,GAAG,YAAG,CAAC,GAAG,CAAC,CAAC;gBACzB,MAAM,aAAa,GAAG,SAAS,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,EAAE,CAAC,YAAG,CAAC,QAAQ,CAAC,CAAC,CAAC;gBACjE,IAAI,CAAC,gBAAgB;qBAClB,cAAc,EAAE;qBAChB,IAAI,CAAC,cAAc,OAAO,mBAAmB,aAAa,EAAE,CAAC,CAAC;YACnE,CAAC;YACD,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACX,IAAI,CAAC,gBAAgB,CAAC,cAAc,EAAE,CAAC,KAAK,CAAC,CAAU,CAAC,CAAC;YACzD,MAAM,CAAC,CAAC;QACV,CAAC;IACH,CAAC;IAED;;;;;;;;;;OAUG;IACI,KAAK,CAAC,QAAQ,CAAC,GAAG,IAAW;QAClC,IAAI,CAAC;YACH,MAAM,IAAA,sBAAK,EAAC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;YACrC,MAAM,IAAI,CAAC,gBAAgB,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,CAAC;YAC5D,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,YAAG,CAAC,GAAG,CAAC,CAAC,CAAC;YAC7C,IAAI,CAAC,gBAAgB,CAAC,cAAc,EAAE,CAAC,IAAI,CAAC,gBAAgB,QAAQ,EAAE,CAAC,CAAC;YACxE,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACX,IAAI,CAAC,gBAAgB,CAAC,cAAc,EAAE,CAAC,KAAK,CAAC,CAAU,CAAC,CAAC;YACzD,MAAM,CAAC,CAAC;QACV,CAAC;IACH,CAAC;IAED;;;;;;;;;;OAUG;IACI,KAAK,CAAC,UAAU,CAAC,GAAG,IAAW;QACpC,IAAI,CAAC;YACH,MAAM,IAAA,sBAAK,EAAC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;YACrC,MAAM,IAAI,CAAC,gBAAgB,CAAC,WAAW,EAAE,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC,CAAC;YAC9D,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,YAAG,CAAC,GAAG,CAAC,CAAC,CAAC;YAC7C,IAAI,CAAC,gBAAgB,CAAC,cAAc,EAAE,CAAC,IAAI,CAAC,iBAAiB,QAAQ,EAAE,CAAC,CAAC;YACzE,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACX,IAAI,CAAC,gBAAgB,CAAC,cAAc,EAAE,CAAC,KAAK,CAAC,CAAU,CAAC,CAAC;YACzD,MAAM,CAAC,CAAC;QACV,CAAC;IACH,CAAC;CACF;AAvGD,sCAuGC"}