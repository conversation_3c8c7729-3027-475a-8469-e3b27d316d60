{"name": "@nut-tree/libnut", "version": "4.2.0", "description": "libnut is an N-API module for desktop automation with node", "main": "dist/index", "typings": "dist/index", "homepage": "https://nutjs.dev", "author": {"name": "dry Software UG (haftungsbeschränkt)", "email": "<EMAIL>", "url": "https://dry.software"}, "bugs": {"url": "https://github.com/nut-tree/nut.js/issues"}, "keywords": ["GUI", "Automation", "mouse", "keyboard", "screenshot", "image", "desktop", "screen", "recognition", "autohotkey"], "license": "Apache-2.0", "scripts": {"clean": "<PERSON><PERSON><PERSON> dist", "compile": "pnpm run clean && tsc -p .", "compile:dev": "pnpm run clean && tsc -p . --watch", "test": "jest --runInBand", "coverage": "jest --coverage --runInBand", "coverage:clean": "rimraf coverage", "publish:next": "pnpm publish --tag next --no-git-checks", "publish:release": "pnpm publish --no-git-checks", "prepublishOnly": "pnpm run compile", "version": "npm version --no-git-tag -f"}, "engines": {"node": ">=10.15.3"}, "dependencies": {}, "devDependencies": {"@nut-tree/configs": "workspace:*", "@nut-tree/shared": "workspace:*", "@nut-tree/provider-interfaces": "workspace:*"}, "peerDependencies": {"@nut-tree/nut-js": "^3"}}