"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Button = void 0;
/**
 * {@link Button} enum represents clickable buttons of a mouse
 */
var Button;
(function (Button) {
    Button[Button["LEFT"] = 0] = "LEFT";
    But<PERSON>[Button["MIDDLE"] = 1] = "MIDDLE";
    Button[Button["RIGHT"] = 2] = "RIGHT";
})(Button || (exports.Button = Button = {}));
//# sourceMappingURL=button.enum.js.map