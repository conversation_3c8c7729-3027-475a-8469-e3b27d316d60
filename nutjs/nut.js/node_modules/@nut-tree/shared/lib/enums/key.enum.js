"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Key = void 0;
/**
 * The {@link Key} enum represents keys of a standard 105 key US layout keyboard
 */
var Key;
(function (Key) {
    Key[Key["Escape"] = 0] = "Escape";
    Key[Key["F1"] = 1] = "F1";
    Key[Key["F2"] = 2] = "F2";
    Key[Key["F3"] = 3] = "F3";
    Key[Key["F4"] = 4] = "F4";
    Key[Key["F5"] = 5] = "F5";
    Key[Key["F6"] = 6] = "F6";
    Key[Key["F7"] = 7] = "F7";
    Key[Key["F8"] = 8] = "F8";
    Key[Key["F9"] = 9] = "F9";
    Key[Key["F10"] = 10] = "F10";
    Key[Key["F11"] = 11] = "F11";
    Key[Key["F12"] = 12] = "F12";
    Key[Key["F13"] = 13] = "F13";
    Key[Key["F14"] = 14] = "F14";
    Key[Key["F15"] = 15] = "F15";
    Key[Key["F16"] = 16] = "F16";
    Key[Key["F17"] = 17] = "F17";
    Key[Key["F18"] = 18] = "F18";
    Key[Key["F19"] = 19] = "F19";
    Key[Key["F20"] = 20] = "F20";
    Key[Key["F21"] = 21] = "F21";
    Key[Key["F22"] = 22] = "F22";
    Key[Key["F23"] = 23] = "F23";
    Key[Key["F24"] = 24] = "F24";
    Key[Key["Print"] = 25] = "Print";
    Key[Key["ScrollLock"] = 26] = "ScrollLock";
    Key[Key["Pause"] = 27] = "Pause";
    Key[Key["Grave"] = 28] = "Grave";
    Key[Key["Num1"] = 29] = "Num1";
    Key[Key["Num2"] = 30] = "Num2";
    Key[Key["Num3"] = 31] = "Num3";
    Key[Key["Num4"] = 32] = "Num4";
    Key[Key["Num5"] = 33] = "Num5";
    Key[Key["Num6"] = 34] = "Num6";
    Key[Key["Num7"] = 35] = "Num7";
    Key[Key["Num8"] = 36] = "Num8";
    Key[Key["Num9"] = 37] = "Num9";
    Key[Key["Num0"] = 38] = "Num0";
    Key[Key["Minus"] = 39] = "Minus";
    Key[Key["Equal"] = 40] = "Equal";
    Key[Key["Backspace"] = 41] = "Backspace";
    Key[Key["Insert"] = 42] = "Insert";
    Key[Key["Home"] = 43] = "Home";
    Key[Key["PageUp"] = 44] = "PageUp";
    Key[Key["NumLock"] = 45] = "NumLock";
    Key[Key["Divide"] = 46] = "Divide";
    Key[Key["Multiply"] = 47] = "Multiply";
    Key[Key["Subtract"] = 48] = "Subtract";
    Key[Key["Tab"] = 49] = "Tab";
    Key[Key["Q"] = 50] = "Q";
    Key[Key["W"] = 51] = "W";
    Key[Key["E"] = 52] = "E";
    Key[Key["R"] = 53] = "R";
    Key[Key["T"] = 54] = "T";
    Key[Key["Y"] = 55] = "Y";
    Key[Key["U"] = 56] = "U";
    Key[Key["I"] = 57] = "I";
    Key[Key["O"] = 58] = "O";
    Key[Key["P"] = 59] = "P";
    Key[Key["LeftBracket"] = 60] = "LeftBracket";
    Key[Key["RightBracket"] = 61] = "RightBracket";
    Key[Key["Backslash"] = 62] = "Backslash";
    Key[Key["Delete"] = 63] = "Delete";
    Key[Key["End"] = 64] = "End";
    Key[Key["PageDown"] = 65] = "PageDown";
    Key[Key["NumPad7"] = 66] = "NumPad7";
    Key[Key["NumPad8"] = 67] = "NumPad8";
    Key[Key["NumPad9"] = 68] = "NumPad9";
    Key[Key["Add"] = 69] = "Add";
    Key[Key["CapsLock"] = 70] = "CapsLock";
    Key[Key["A"] = 71] = "A";
    Key[Key["S"] = 72] = "S";
    Key[Key["D"] = 73] = "D";
    Key[Key["F"] = 74] = "F";
    Key[Key["G"] = 75] = "G";
    Key[Key["H"] = 76] = "H";
    Key[Key["J"] = 77] = "J";
    Key[Key["K"] = 78] = "K";
    Key[Key["L"] = 79] = "L";
    Key[Key["Semicolon"] = 80] = "Semicolon";
    Key[Key["Quote"] = 81] = "Quote";
    Key[Key["Return"] = 82] = "Return";
    Key[Key["NumPad4"] = 83] = "NumPad4";
    Key[Key["NumPad5"] = 84] = "NumPad5";
    Key[Key["NumPad6"] = 85] = "NumPad6";
    Key[Key["LeftShift"] = 86] = "LeftShift";
    Key[Key["Z"] = 87] = "Z";
    Key[Key["X"] = 88] = "X";
    Key[Key["C"] = 89] = "C";
    Key[Key["V"] = 90] = "V";
    Key[Key["B"] = 91] = "B";
    Key[Key["N"] = 92] = "N";
    Key[Key["M"] = 93] = "M";
    Key[Key["Comma"] = 94] = "Comma";
    Key[Key["Period"] = 95] = "Period";
    Key[Key["Slash"] = 96] = "Slash";
    Key[Key["RightShift"] = 97] = "RightShift";
    Key[Key["Up"] = 98] = "Up";
    Key[Key["NumPad1"] = 99] = "NumPad1";
    Key[Key["NumPad2"] = 100] = "NumPad2";
    Key[Key["NumPad3"] = 101] = "NumPad3";
    Key[Key["Enter"] = 102] = "Enter";
    Key[Key["LeftControl"] = 103] = "LeftControl";
    Key[Key["LeftSuper"] = 104] = "LeftSuper";
    Key[Key["LeftWin"] = 105] = "LeftWin";
    Key[Key["LeftCmd"] = 106] = "LeftCmd";
    Key[Key["LeftAlt"] = 107] = "LeftAlt";
    Key[Key["Space"] = 108] = "Space";
    Key[Key["RightAlt"] = 109] = "RightAlt";
    Key[Key["RightSuper"] = 110] = "RightSuper";
    Key[Key["RightWin"] = 111] = "RightWin";
    Key[Key["RightCmd"] = 112] = "RightCmd";
    Key[Key["Menu"] = 113] = "Menu";
    Key[Key["RightControl"] = 114] = "RightControl";
    Key[Key["Fn"] = 115] = "Fn";
    Key[Key["Left"] = 116] = "Left";
    Key[Key["Down"] = 117] = "Down";
    Key[Key["Right"] = 118] = "Right";
    Key[Key["NumPad0"] = 119] = "NumPad0";
    Key[Key["Decimal"] = 120] = "Decimal";
    Key[Key["Clear"] = 121] = "Clear";
    Key[Key["AudioMute"] = 122] = "AudioMute";
    Key[Key["AudioVolDown"] = 123] = "AudioVolDown";
    Key[Key["AudioVolUp"] = 124] = "AudioVolUp";
    Key[Key["AudioPlay"] = 125] = "AudioPlay";
    Key[Key["AudioStop"] = 126] = "AudioStop";
    Key[Key["AudioPause"] = 127] = "AudioPause";
    Key[Key["AudioPrev"] = 128] = "AudioPrev";
    Key[Key["AudioNext"] = 129] = "AudioNext";
    Key[Key["AudioRewind"] = 130] = "AudioRewind";
    Key[Key["AudioForward"] = 131] = "AudioForward";
    Key[Key["AudioRepeat"] = 132] = "AudioRepeat";
    Key[Key["AudioRandom"] = 133] = "AudioRandom";
})(Key || (exports.Key = Key = {}));
//# sourceMappingURL=key.enum.js.map