{"name": "@nut-tree/shared", "version": "4.2.0", "description": "Shared objects, data types and functions for @nut-tree/nut-js", "main": "dist/index", "typings": "dist/index", "scripts": {"test": "jest --runInBand", "coverage": "jest --coverage --runInBand --logHeapUsage", "coverage:clean": "rimraf coverage", "clean": "<PERSON><PERSON><PERSON> dist", "compile": "pnpm run clean && tsc -p .", "publish:next": "pnpm publish --tag next --no-git-checks", "publish:release": "pnpm publish --no-git-checks", "prepublishOnly": "pnpm run compile", "typedoc": "typedoc --out docs"}, "dependencies": {"node-abort-controller": "3.1.1", "jimp": "0.22.10"}, "devDependencies": {"@nut-tree/configs": "workspace:*"}, "keywords": ["nut-js"], "author": {"name": "dry Software UG (haftungsbeschränkt)", "email": "<EMAIL>", "url": "https://dry.software"}, "license": "Apache-2.0"}