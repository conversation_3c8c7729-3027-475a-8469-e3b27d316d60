{"name": "@nut-tree/provider-interfaces", "version": "4.2.0", "description": "Public provider interfaces for @nut-tree/nut-js", "main": "dist/index", "typings": "dist/index", "scripts": {"clean": "<PERSON><PERSON><PERSON> dist", "compile": "pnpm run clean && tsc -p .", "publish:next": "pnpm publish --tag next --no-git-checks", "publish:release": "pnpm publish --no-git-checks", "prepublishOnly": "pnpm run compile", "typedoc": "typedoc --out docs"}, "keywords": ["nut-js", "provider"], "author": {"name": "dry Software UG (haftungsbeschränkt)", "email": "<EMAIL>", "url": "https://dry.software"}, "license": "Apache-2.0", "dependencies": {"@nut-tree/shared": "workspace:*"}, "devDependencies": {"@nut-tree/configs": "workspace:*"}}