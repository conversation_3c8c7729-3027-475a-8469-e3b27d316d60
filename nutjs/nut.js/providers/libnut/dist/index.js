"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DefaultWindowAction = exports.DefaultScreenAction = exports.DefaultMouseAction = exports.DefaultKeyboardAction = void 0;
const lib_1 = require("./lib");
Object.defineProperty(exports, "DefaultKeyboardAction", { enumerable: true, get: function () { return lib_1.KeyboardAction; } });
Object.defineProperty(exports, "DefaultMouseAction", { enumerable: true, get: function () { return lib_1.MouseAction; } });
Object.defineProperty(exports, "DefaultScreenAction", { enumerable: true, get: function () { return lib_1.ScreenAction; } });
Object.defineProperty(exports, "DefaultWindowAction", { enumerable: true, get: function () { return lib_1.WindowAction; } });
//# sourceMappingURL=index.js.map